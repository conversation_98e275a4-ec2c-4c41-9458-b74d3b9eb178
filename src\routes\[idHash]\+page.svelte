<script lang="ts">
	import { page } from "$app/state";
	import { UnauthorizedError } from "$lib/models/result/unauthorizedError";
	import { RoomService } from "$lib/services/roomService";
	import { error, type NumericRange } from "@sveltejs/kit";
	import { onDestroy, onMount, untrack } from "svelte";
	import * as signalR from "@microsoft/signalr";
	import type { ResultDto, ValueResultDto } from "$lib/models/dto/socket/resultDto";
	import { resultFromDto, valueResultFromDto } from "$lib/utils/resultFromDto";
	import { fail, ok, type Result, type ValueResult } from "$lib/models/result/result";
	import { variables } from "$lib/variables";
	import UserList from "$lib/components/ui/user-list/UserList.svelte";
	import { RoomUserRoles, type RoomUser } from "$lib/models/roomUser";
	import type { SourceContainer, SubtitleSource } from "$lib/models/sourceContainer";
	import { isMovieInfo, isMovieOrSeriesInfo, isSeriesInfo } from "$lib/models/sourceContainer";
	import {
		isSeekByKeyboardSource,
		PlaybackState,
		type PlaybackInfo
	} from "$lib/models/playbackInfo";
	import { sleep } from "$lib/utils/sleep";
	import MediaPlayer from "$lib/components/player/MediaPlayer.svelte";
	import {
		getRoomUsersState,
		RoomUsersState,
		setRoomUsersState
	} from "$lib/states/roomUsersState.svelte";
	import { AppUserState, getAppUserState } from "$lib/states/appUserState.svelte";
	import {
		CurrentRoomUserState,
		getCurrentRoomUserState,
		setCurrentRoomUserState
	} from "$lib/states/currentRoomUserState.svelte";
	import type { MediaPlayRequestEvent } from "vidstack";
	import { getHeaderState, type HeaderState } from "$lib/states/headerState.svelte";
	import { AddVideoEvent, getAddVideoEvent } from "$lib/states/videoInputState.svelte";
	import VideoInfo from "$lib/components/ui/video-info/VideoInfo.svelte";
	import Logo from "$lib/components/ui/logo/Logo.svelte";
	import { AppUserService } from "$lib/services/appUserService";
	import { CancellationToken, retryResult } from "$lib/utils/retry";
	import { CancelledError } from "$lib/models/result/cancelledError";
	import { displayError } from "$lib/utils/toastUtils";
	import { PersistentStorage } from "$lib/storage/persistentStorage.svelte";
	import { CookieStorageProvider } from "$lib/storage/cookieStorageProvider.js";
	import { browser } from "$app/environment";
	import PlugsIcon from "~icons/phosphor-icons-fill/plugs-fill";
	import { formatEpisodeNumber } from "$lib/utils/formatEpisodeNumber";

	const SHOW_MUTED_PLAYBACK_BANNER = true;
	const socksUrl = variables.SocksUrl;
	const initialSynchronizationDelayMs = 3 * 1000;
	const periodicalSynchronizationDelayMs = 60 * 1000;
	const minAutomaticSynchronizationDelayMs = 100;
	const maxDeltaForFluidSynchronizationMs = 1000;
	const retryConnectAndJoinPreMessageDelayMs = 500;
	const retryConnectAndJoinPostMessageDelayMs = 2500;

	let { data } = $props();

	setCurrentRoomUserState();
	setRoomUsersState();

	const roomService = RoomService.getInstance();
	const appUserService = AppUserService.getInstance();
	const appUserState: AppUserState = getAppUserState();
	const currentRoomUserState: CurrentRoomUserState = getCurrentRoomUserState();
	const roomUsersState: RoomUsersState = getRoomUsersState();
	const headerState: HeaderState = getHeaderState();
	const addVideoEvent: AddVideoEvent = getAddVideoEvent();
	const connectAndJoinCt: CancellationToken = new CancellationToken();
	const isTheatreModeStorage: PersistentStorage<boolean> = new PersistentStorage(
		"isTheatreMode",
		false,
		new CookieStorageProvider()
	);
	// In which mode to render the player initially
	// This is needed to avoid SSR player flash
	const isRenderPlayerInWideContainer = data.isTheatreMode ?? false;
	addVideoEvent.on(onClickPlay);

	let currentSourceContainer = $state<SourceContainer>();
	let isReconnecting = $state(false);
	// let isTheatreMode = $state(false);
	let currentUser = $derived(appUserState.user);
	let showSubtitleShiftMenu = $derived.by(() => {
		if (!currentRoomUserState.user) {
			return false;
		}

		return currentRoomUserState.user.role >= RoomUserRoles.Administrator;
	});

	// @ts-ignore
	let player: MediaPlayer;
	let idHash = page.params.idHash;
	let isTestMode = "testSourceContainer" in page.params;
	let currentSynchronizationTimeout: NodeJS.Timeout;
	let currentSynchronizationInterval: NodeJS.Timeout;
	let connection: signalR.HubConnection;
	let defaultPlayerContainerEl: HTMLDivElement;
	let widePlayerContainerEl: HTMLDivElement;
	let playerWrapperEl: HTMLDivElement;
	let isAwaitingSynchronization = false;
	let lastBackdropPlaceholderUrl: string | undefined;
	let backdropPlaceholderSvg: string | undefined = $state(data.backdropPlaceholderSvg);

	if (data.room.currentSourceContainer) {
		if (
			data.room.currentSourceContainer.externalInfo &&
			isMovieOrSeriesInfo(data.room.currentSourceContainer.externalInfo) &&
			data.room.currentSourceContainer.externalInfo.backdropPlaceholderUrl
		) {
			lastBackdropPlaceholderUrl =
				data.room.currentSourceContainer.externalInfo.backdropPlaceholderUrl;
		}

		mapSourceContainerExternalInfoType(data.room.currentSourceContainer);
		currentSourceContainer = data.room.currentSourceContainer;
	}

	isTheatreModeStorage.value = isRenderPlayerInWideContainer;

	$effect(() => {
		if (!currentRoomUserState.user) {
			return;
		}

		const isAdminOrHigher = currentRoomUserState.user.role >= RoomUserRoles.Administrator;
		headerState.isVideoInputVisible = isAdminOrHigher;
	});

	$effect(() => {
		currentSourceContainer;
		untrack(() => setBackdropPlaceholderSvg(currentSourceContainer));
	});

	onMount(async () => {
		if (isTestMode) {
			initiateTestMode();
			return;
		}

		window.addEventListener("beforeunload", onWindowBeforeUnload);
		await connectAndJoinRoom();
	});

	onDestroy(() => {
		if (browser) {
			window.removeEventListener("beforeunload", onWindowBeforeUnload);
		}

		headerState.isVideoInputVisible = false;
		addVideoEvent.off(onClickPlay);

		disposeConnections();
	});

	function disposeConnections() {
		connectAndJoinCt.cancel();
		appUserService.waitForUserCt?.cancel();
		connection?.stop();

		connection?.off("userEnteredRoom");
		connection?.off("userLeftRoom");
		connection?.off("currentSourceChanged");
		connection?.off("playbackStateChanged");
		connection?.off("subtitlesChanged");
		connection?.off("roomUserChanged");
	}

	function initiateTestMode(): void {
		currentSourceContainer = JSON.parse(page.params.testSourceContainer);
	}

	async function connect(): Promise<Result> {
		if (!currentUser) {
			await appUserService.waitForInitializedUser();
		}

		return establishConnection();
	}

	async function connectAndJoinRoom(): Promise<void> {
		// TODO: Not infinite?
		const connectResult = await retryResult(
			connect,
			Number.POSITIVE_INFINITY,
			1000,
			async (error) => {
				displayError("Failed to connect to the room", error, true);
				if (error instanceof UnauthorizedError) {
					appUserService.removeCurrentUser();
				}
			},
			connectAndJoinCt
		);
		if (connectResult.isFailed) {
			if (connectResult.error instanceof CancelledError) {
				return;
			}

			displayError("Failed to connect to the room", connectResult.error);
			return;
		}

		const joinRoomResult = await joinRoom();
		if (joinRoomResult.isFailed) {
			displayError("Failed to join the room", joinRoomResult.error);
			return;
		}

		getCurrentRoomUser().then((result) => {
			if (result.isFailed) {
				displayError("Failed to get current room user", result.error);
				return;
			}

			const currentRoomUser = result.value;
			if (appUserState.user) {
				if (currentRoomUser.username !== appUserState.user.username) {
					appUserService.refreshUser();
				}
			}

			currentRoomUserState.user = currentRoomUser;
		});

		getCurrentSource().then(async (result) => {
			if (result.isFailed) {
				displayError("Failed to get current source", result.error);
				return;
			}

			const sourceContainer = result.value;
			if (sourceContainer) {
				mapSourceContainerExternalInfoType(sourceContainer);
			}

			currentSourceContainer = sourceContainer;

			if (player.getCanPlay()) {
				await fetchAndSynchronizePlayback(false);
			} else if (!isAwaitingSynchronization) {
				isAwaitingSynchronization = true;
				await player.waitUntilCanPlay();
				isAwaitingSynchronization = false;

				await fetchAndSynchronizePlayback(false);
			}
		});

		getAllConnectedRoomUsers().then((result) => {
			if (result.isFailed) {
				displayError("Failed to get all connected room users", result.error);
				return;
			}

			roomUsersState.set(result.value);
		});
	}

	async function getCurrentRoomUser(): Promise<ValueResult<RoomUser>> {
		const getCurrentRoomUserResultDto = await connection.invoke<ValueResultDto<RoomUser>>(
			"getCurrentRoomUser",
			idHash
		);
		const result = valueResultFromDto(getCurrentRoomUserResultDto);

		return result;
	}

	async function getAllConnectedRoomUsers(): Promise<ValueResult<RoomUser[]>> {
		const getAllConnectedRoomUsersResultDto = await connection.invoke<ValueResultDto<RoomUser[]>>(
			"getAllConnectedRoomUsers",
			idHash
		);
		const result = valueResultFromDto(getAllConnectedRoomUsersResultDto);

		return result;
	}

	async function getCurrentAppUserToken(): Promise<string> {
		let token;
		if (currentUser) {
			token = currentUser.token;
		} else {
			const appUser = await appUserService.waitForInitializedUser();
			token = appUser.token;
		}

		return token;
	}

	async function establishConnection(): Promise<Result> {
		try {
			const token = await getCurrentAppUserToken();
			connection = new signalR.HubConnectionBuilder()
				.withUrl(socksUrl, {
					accessTokenFactory: () => token
				})
				.build();

			registerConnectionEventHandlers();

			await connection.start();
			return ok();
		} catch (error) {
			const errorStr = String(error);
			if (errorStr.includes("401")) {
				const resultError = new UnauthorizedError(errorStr).withMetadata("statusCode", 401);
				return fail(resultError);
			}

			return fail(`Failed to establish a connection: ${error}`);
		}
	}

	async function getCurrentSource(): Promise<ValueResult<SourceContainer>> {
		const getCurrentSourceResult = await connection.invoke<ValueResultDto<SourceContainer>>(
			"getCurrentSource",
			idHash
		);
		const result = valueResultFromDto(getCurrentSourceResult);

		return result;
	}

	async function getPlaybackInfo(): Promise<ValueResult<PlaybackInfo>> {
		const getPlaybackInfoResult = await connection.invoke<ValueResultDto<PlaybackInfo>>(
			"getPlaybackInfo",
			idHash
		);
		const result = valueResultFromDto(getPlaybackInfoResult);

		return result;
	}

	function registerConnectionEventHandlers(): void {
		connection.onclose(onSocketConnectionClose);

		connection.on("userEnteredRoom", onUserEnteredRoom);
		connection.on("userLeftRoom", onUserLeftRoom);
		connection.on("currentSourceChanged", onCurrentSourceChanged);
		connection.on("playbackStateChanged", onPlaybackStateChanged);
		connection.on("subtitlesChanged", onSubtitlesChanged);
		connection.on("roomUserChanged", onRoomUserChanged);
	}

	async function joinRoom(): Promise<Result> {
		const token = await getCurrentAppUserToken();
		const joinRoomResult = await roomService.joinRoom(idHash, token);
		if (joinRoomResult.isFailed) {
			if (joinRoomResult.error.hasMetadataKey("statusCode")) {
				const statusCode =
					joinRoomResult.error.getMetadataValue<NumericRange<400, 599>>("statusCode");
				throw error(statusCode, joinRoomResult.error.message);
			}

			throw error(500, joinRoomResult.error.message);
		}

		const resultDto = await connection.invoke<ResultDto>("joinRoom", idHash);
		const result = resultFromDto(resultDto);

		return result;
	}

	async function retryConnectAndJoinRoom(): Promise<void> {
		await sleep(retryConnectAndJoinPreMessageDelayMs);
		isReconnecting = true;
		await sleep(retryConnectAndJoinPostMessageDelayMs);

		return connectAndJoinRoom();
	}

	async function fetchAndSynchronizePlayback(fluidSynchronization?: boolean) {
		// wait until player ready here?
		const timestampStart = performance.now();
		const getPlaybackInfoResult = await getPlaybackInfo();
		if (getPlaybackInfoResult.isFailed) {
			return;
		}

		const timestampEnd = performance.now();
		const latencyMs = timestampEnd - timestampStart;
		const approximatePingMs = latencyMs / 2;
		const approximatePingSecs = approximatePingMs / 1000;
		const playbackInfo = getPlaybackInfoResult.value;
		playbackInfo.currentTimeInSeconds += approximatePingSecs;

		synchronizePlayback(playbackInfo, fluidSynchronization);
	}

	function synchronizePlayback(playbackInfo: PlaybackInfo, fluidSynchronization?: boolean): void {
		if (playbackInfo.playbackState === player.getPlaybackState()) {
			synchronizeMatchingPlaybackState(playbackInfo, fluidSynchronization);
			return;
		}

		synchronizeMismatchingPlaybackState(playbackInfo);
	}

	function synchronizeMatchingPlaybackState(
		playbackInfo: PlaybackInfo,
		fluidSynchronization?: boolean
	): void {
		const currentTimeSecs = player.getCurrentTime();
		const synchronizationDeltaSecs = playbackInfo.currentTimeInSeconds - currentTimeSecs;
		const synchronizationDeltaMs = synchronizationDeltaSecs * 1000;
		const abosluteSynchronizationDeltaMs = Math.abs(synchronizationDeltaMs);

		if (abosluteSynchronizationDeltaMs < minAutomaticSynchronizationDelayMs) {
			return;
		}

		switch (playbackInfo.playbackState) {
			case PlaybackState.Playing:
				if (abosluteSynchronizationDeltaMs > maxDeltaForFluidSynchronizationMs) {
					fluidSynchronization = false;
				}

				player.synchronizePlayback(synchronizationDeltaSecs, fluidSynchronization);
				break;
			default:
				player.setCurrentTime(playbackInfo.currentTimeInSeconds);
				break;
		}
	}

	function synchronizeMismatchingPlaybackState(playbackInfo: PlaybackInfo): void {
		const currentTimeSecs = player.getCurrentTime();
		const synchronizationDeltaSecs = playbackInfo.currentTimeInSeconds - currentTimeSecs;
		const synchronizationDeltaMs = synchronizationDeltaSecs * 1000;
		const abosluteSynchronizationDeltaMs = Math.abs(synchronizationDeltaMs);

		switch (playbackInfo.playbackState) {
			case PlaybackState.Stopped:
				player.stop();
				break;
			case PlaybackState.Paused:
				player.pause();
				if (abosluteSynchronizationDeltaMs >= minAutomaticSynchronizationDelayMs) {
					player.setCurrentTime(playbackInfo.currentTimeInSeconds);
				}
				break;
			case PlaybackState.Playing:
				if (abosluteSynchronizationDeltaMs >= minAutomaticSynchronizationDelayMs) {
					player.setCurrentTime(playbackInfo.currentTimeInSeconds);
				}
				player.play();
				break;
		}
	}

	function clearScheduledSynchronization() {
		clearTimeout(currentSynchronizationTimeout);
		clearInterval(currentSynchronizationInterval);
	}

	function mapSourceContainerExternalInfoType(sourceContainer: SourceContainer) {
		if (sourceContainer.externalInfo) {
			if ("$external_info_type" in sourceContainer.externalInfo) {
				sourceContainer.externalInfo.type = sourceContainer.externalInfo[
					"$external_info_type"
				] as string;
			}
		}
	}

	async function setBackdropPlaceholderSvg(sourceContainer: SourceContainer | undefined) {
		if (!sourceContainer) {
			return;
		}

		if (
			sourceContainer.externalInfo &&
			isMovieOrSeriesInfo(sourceContainer.externalInfo) &&
			sourceContainer.externalInfo.backdropPlaceholderUrl
		) {
			if (sourceContainer.externalInfo.backdropPlaceholderUrl === lastBackdropPlaceholderUrl) {
				return;
			}

			let backdropPlaceholderSvgResult = await roomService.getSvg(
				sourceContainer.externalInfo.backdropPlaceholderUrl
			);

			if (backdropPlaceholderSvgResult.isSuccess) {
				lastBackdropPlaceholderUrl = sourceContainer.externalInfo.backdropPlaceholderUrl;
				backdropPlaceholderSvg = backdropPlaceholderSvgResult.value;
			}
		}
	}

	async function onSocketConnectionClose(): Promise<void> {
		roomUsersState.clear();
		await retryConnectAndJoinRoom();
	}

	async function onClickPlay(videoUrl: string): Promise<void> {
		const resultDto = await connection.invoke<ResultDto>("setVideo", idHash, videoUrl);
		const result = resultFromDto(resultDto);
		if (result.isFailed) {
			displayError("Failed to set video", result.error);
		}
	}

	function onUserEnteredRoom(user: RoomUser): void {
		roomUsersState.add(user);
	}

	function onUserLeftRoom(user: RoomUser): void {
		roomUsersState.remove(user);
	}

	function onCurrentSourceChanged(sourceContainer: SourceContainer): void {
		if (sourceContainer) {
			mapSourceContainerExternalInfoType(sourceContainer);
		}

		// HACK: Pause the player before changing the source
		// This is needed to prevent the player from corrupting and requiring manual pause/unpause
		if (player.getPlaybackState() === PlaybackState.Playing) {
			player.pause().then(() => {
				player.resetSubtitles();
				currentSourceContainer = sourceContainer;
			});
		} else {
			player.resetSubtitles();
			currentSourceContainer = sourceContainer;
		}
	}

	async function onPlaybackStateChanged(playbackInfo: PlaybackInfo) {
		if (player.getCanPlay()) {
			if (isSeekByKeyboardSource(playbackInfo.playbackStateSource)) {
				player.addSeekedSeconds(playbackInfo.playbackStateSource.keyboardSeekDeltaSeconds);
				synchronizePlayback(playbackInfo, false);
			} else {
				synchronizePlayback(playbackInfo);
			}
		} else if (!isAwaitingSynchronization) {
			isAwaitingSynchronization = true;
			await player.waitUntilCanPlay();
			isAwaitingSynchronization = false;

			await fetchAndSynchronizePlayback(false);
		}
	}

	async function onSubtitlesChanged(subtitleSource: SubtitleSource) {
		player.setSubtitleOffset(subtitleSource.url, subtitleSource.offset);
	}

	async function onRoomUserChanged(roomUser: RoomUser) {
		roomUsersState.update(roomUser);
		if (currentRoomUserState.user?.id === roomUser.id) {
			currentRoomUserState.user = roomUser;
		}

		if (appUserState.user?.id === roomUser.id) {
			if (appUserState.user.username !== roomUser.username) {
				appUserService.refreshUser();
			}
		}
	}

	function onPlayerPlayByUser(event: MediaPlayRequestEvent) {
		// Ignore "media-play-request" when the player goes through dom connect
		// which happens when we toggle theatre mode
		if (event.triggers.chain[0].type === "dom-connect") {
			event.preventDefault();
			return;
		}

		connection.invoke("playSource", idHash);
	}

	function onPlayerPauseByUser() {
		const currentTimeInSeconds = player.getCurrentTime();
		connection.invoke("pauseSource", idHash, currentTimeInSeconds);
	}

	function onPlayerSeekingByUser(seconds: number, keyboardSeekDeltaSeconds: number) {
		connection.invoke("skipSourceTo", idHash, seconds, keyboardSeekDeltaSeconds);
	}

	function onPlayerSetSubtitleOffsetByUser(src: string, offset: number) {
		connection.invoke("setSubtitlesOffset", idHash, src, offset);
	}

	async function onSetUserRole(user: RoomUser, newRole: RoomUserRoles) {
		const oldRole = user.role;
		user.role = newRole;
		roomUsersState.update(user);

		const result = await connection.invoke<ResultDto>("setUserRole", idHash, user.id, newRole);
		if (!result.isSuccess) {
			user.role = oldRole;
			roomUsersState.update(user);
		}
	}

	function onPlaying() {
		clearScheduledSynchronization();
		currentSynchronizationTimeout = setTimeout(async () => {
			await fetchAndSynchronizePlayback(true);

			currentSynchronizationInterval = setInterval(async () => {
				await fetchAndSynchronizePlayback(true);
			}, periodicalSynchronizationDelayMs);
		}, initialSynchronizationDelayMs);
	}

	function onVideoHalted() {
		clearScheduledSynchronization();
	}

	function onToggleTheatreMode(newValue: boolean): void {
		const playbackState: PlaybackState = player.getPlaybackState();
		isTheatreModeStorage.value = newValue;

		if (isTheatreModeStorage.value) {
			widePlayerContainerEl.appendChild(playerWrapperEl);
		} else {
			defaultPlayerContainerEl.appendChild(playerWrapperEl);
		}

		// A hack that restores the player to the correct state after toggling theatre mode
		// This is due to dom connect messing with player
		// I think we don't see controls anymore when video is paused,
		// is because player fails to setup mouse over event?
		setTimeout(() => {
			if (playbackState === PlaybackState.Playing) {
				player.play();
			} else if (playbackState === PlaybackState.Paused) {
				const currentTimeInSeconds = player.getCurrentTime();
				const wasMuted = player.isMuted();
				player.mute();
				player.waitUntilMuted().then(() =>
					player.play().then(() =>
						player.pause().then(() => {
							setTimeout(() => {
								player.setCurrentTime(currentTimeInSeconds);
								if (!wasMuted) {
									player.unmute();
								}
							}, 0);
						})
					)
				);
			} else if (playbackState === PlaybackState.Stopped) {
				player.stop();
			}
		}, 0);
	}

	function onWindowBeforeUnload() {
		disposeConnections();
	}
</script>

<svelte:head>
	{#if currentSourceContainer && isMovieOrSeriesInfo(currentSourceContainer.externalInfo)}
		{@const description =
			isSeriesInfo(currentSourceContainer.externalInfo) &&
			currentSourceContainer.externalInfo.episodeNumber
				? `${formatEpisodeNumber(
						currentSourceContainer.externalInfo.episodeNumber,
						currentSourceContainer.externalInfo.seasonNumber
					)}\n\n${currentSourceContainer.externalInfo.description ?? ""}`
				: `${currentSourceContainer.externalInfo.description ?? ""}`}
		{@const title = `${currentSourceContainer.externalInfo.title ?? ""}${
			isMovieInfo(currentSourceContainer.externalInfo) &&
			currentSourceContainer.externalInfo.releaseDate
				? ` (${new Date(currentSourceContainer.externalInfo.releaseDate).getFullYear()})`
				: ""
		}`}
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={currentSourceContainer.externalInfo.posterImageUrl ?? ""} />
		<meta property="og:image:alt" content={currentSourceContainer.externalInfo.title ?? ""} />
		<meta property="og:type" content="video.movie" />
		<meta property="og:url" content={page.url.href} />
		<title
			>{currentSourceContainer.externalInfo.title
				? `${currentSourceContainer.externalInfo.title} - watch`
				: "watch"}</title
		>
	{:else}
		<title>watch</title>
	{/if}
</svelte:head>

{#snippet videoPlayer()}
	<div
		class="relative grid grid-rows-1 bg-popover overflow-hidden {isTheatreModeStorage.value
			? 'player-wide'
			: 'player-default rounded-xl'}"
		bind:this={playerWrapperEl}
	>
		<div
			class="row-start-1 col-start-1 h-full bg-transparent"
			style="aspect-ratio: var(--ratio);"
		></div>
		<div class="absolute inset-0 flex items-center justify-center z-0">
			<Logo size="lg" className="text-player-neutral-700 opacity-50"></Logo>
		</div>
		{#if backdropPlaceholderSvg}
			<div class="absolute inset-0 z-0">
				{@html backdropPlaceholderSvg}
			</div>
		{/if}
		<div
			class="row-start-1 col-start-1 w-full transition-all duration-200 {currentSourceContainer
				? 'opacity-100'
				: 'opacity-0 hidden'} {isTheatreModeStorage.value ? '' : 'h-full'}"
		>
			<MediaPlayer
				bind:this={player}
				source={currentSourceContainer}
				isTheatreMode={isTheatreModeStorage.value}
				showMutedPlaybackBanner={SHOW_MUTED_PLAYBACK_BANNER}
				{showSubtitleShiftMenu}
				{onToggleTheatreMode}
				onPlayByUser={onPlayerPlayByUser}
				onPauseByUser={onPlayerPauseByUser}
				onSeekingByUser={onPlayerSeekingByUser}
				{onPlaying}
				onEnded={onVideoHalted}
				onPause={onVideoHalted}
				onStalled={onVideoHalted}
				onWaiting={onVideoHalted}
				onSetSubtitleOffsetByUser={onPlayerSetSubtitleOffsetByUser}
			></MediaPlayer>
		</div>
	</div>
{/snippet}

<div
	style="--ratio: calc({currentSourceContainer?.video?.width &&
	currentSourceContainer?.video?.height
		? `${currentSourceContainer.video.width} / ${currentSourceContainer.video.height}`
		: '16 / 9'});"
>
	<div bind:this={widePlayerContainerEl}>
		{#if isRenderPlayerInWideContainer}
			{@render videoPlayer()}
		{/if}
	</div>
	<div
		class="flex justify-center space-x-4 mb-12 {isTheatreModeStorage.value
			? 'max-w-[1600px] mt-4 mx-auto px-2'
			: 'px-2 md:w-min'}"
	>
		<div class:grow={isTheatreModeStorage.value}>
			<div class="default-video-container flex justify-center" bind:this={defaultPlayerContainerEl}>
				{#if !isRenderPlayerInWideContainer}
					{@render videoPlayer()}
				{/if}
			</div>

			<div class={isTheatreModeStorage.value ? "" : "mt-4"}>
				<div
					class="relative shrink-0 w-full h-aside bg-popover rounded-xl shadows-card-level0 overflow-hidden px-2 pb-2 block md:hidden mb-4"
				>
					{#if roomUsersState.users.length === 0}
						<div
							class="absolute inset-0 flex justify-center w-full h-full text-foreground-accent/35"
						>
							<PlugsIcon class="size-7 mt-8"></PlugsIcon>
							<div class="ml-2 mt-8">
								<span class="font-subheader text-xl uppercase">Not connected</span>
							</div>
						</div>
					{/if}
					<UserList {onSetUserRole}></UserList>
				</div>
				{#if currentSourceContainer}
					<VideoInfo
						sourceContainer={currentSourceContainer}
						isEpisodeTitleBlurred={data.isEpisodeTitleBlurred}
					></VideoInfo>
				{/if}
			</div>
		</div>
		<div
			class="relative shrink-0 w-80 h-aside bg-popover rounded-xl shadows-card-level0 overflow-hidden px-2 pb-2 hidden md:block"
		>
			{#if roomUsersState.users.length === 0}
				<div class="absolute inset-0 flex justify-center w-full h-full text-foreground-accent/35">
					<PlugsIcon class="size-7 mt-8"></PlugsIcon>
					<div class="ml-2 mt-8">
						<span class="font-subheader text-xl uppercase">Not connected</span>
					</div>
				</div>
			{/if}
			<UserList {onSetUserRole}></UserList>
		</div>
	</div>
</div>

<style>
	.player-default:not([data-fullscreen=""]) {
		--min-player-height: 200px;
		--max-player-width: calc(100vw - 24rem - 1rem - 1rem);
		--max-player-height: calc(100vh - 5rem - 5rem - 2rem);
		--player-height: max(
			min(calc(var(--max-player-width) / var(--ratio)), var(--max-player-height)),
			var(--min-player-height)
		);
		height: var(--player-height);
		width: calc(var(--player-height) * var(--ratio));
	}

	.player-wide:not([data-fullscreen=""]) {
		--min-player-height: 200px;
		--max-player-height: calc(100vh - 5rem - 5rem - 2rem);
		--player-height: max(min(100%, var(--max-player-height)), var(--min-player-height));
		min-height: var(--min-player-height);
		max-height: var(--max-player-height);
		height: var(--player-height);
		width: 100vw;
	}

	:global(.h-aside) {
		height: 296px;
	}
</style>
